# Generate a random secret: https://generate-secret.vercel.app/32 or `openssl rand -base64 32`
AUTH_SECRET=tunecatch-secret-key-12345678901234567890

# OpenAI API Key for o3 model
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Temporary SQLite database for development (no external DB required)
POSTGRES_URL=postgresql://localhost:5432/tunecatch?sslmode=disable

# Optional: Music service API keys (not required for basic functionality)
# SPOTIFY_CLIENT_ID=your-spotify-client-id
# SPOTIFY_CLIENT_SECRET=your-spotify-client-secret
# LASTFM_API_KEY=your-lastfm-api-key

# Optional: Vercel services (not required for local development)
# BLOB_READ_WRITE_TOKEN=your-blob-token-here
# REDIS_URL=your-redis-url-here

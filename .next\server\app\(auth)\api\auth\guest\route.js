const CHUNK_PUBLIC_PATH = "server/app/(auth)/api/auth/guest/route.js";
const runtime = require("../../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/3dc60_next_c869a822._.js");
runtime.loadChunk("server/chunks/c1962_@auth_core_bf9427bb._.js");
runtime.loadChunk("server/chunks/990b6_jose_dist_node_esm_e7de0bd5._.js");
runtime.loadChunk("server/chunks/ddc0a_drizzle-orm_febb145e._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_25d9413b._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__cbdd7e09._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/(auth)/api/auth/guest/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0-canary.31_@opentelemetry+api@1.9.0_@playwright+test@1.51.0_react-dom@19.0.0-rc-45_mlqzopqn56t6gad76627omqvii/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/(auth)/api/auth/guest/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.0-canary.31_@opentelemetry+api@1.9.0_@playwright+test@1.51.0_react-dom@19.0.0-rc-45_mlqzopqn56t6gad76627omqvii/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/(auth)/api/auth/guest/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;

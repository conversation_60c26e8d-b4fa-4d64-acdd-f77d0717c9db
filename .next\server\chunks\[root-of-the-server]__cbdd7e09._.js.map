{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/lib/db/schema.ts"], "sourcesContent": ["import type { InferSelectModel } from 'drizzle-orm';\r\nimport {\r\n  pgTable,\r\n  varchar,\r\n  timestamp,\r\n  json,\r\n  uuid,\r\n  text,\r\n  primaryKey,\r\n  foreignKey,\r\n  boolean,\r\n} from 'drizzle-orm/pg-core';\r\n\r\nexport const user = pgTable('User', {\r\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\r\n  email: varchar('email', { length: 64 }).notNull(),\r\n  password: varchar('password', { length: 64 }),\r\n});\r\n\r\nexport type User = InferSelectModel<typeof user>;\r\n\r\nexport const chat = pgTable('Chat', {\r\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\r\n  createdAt: timestamp('createdAt').notNull(),\r\n  title: text('title').notNull(),\r\n  userId: uuid('userId')\r\n    .notNull()\r\n    .references(() => user.id),\r\n  visibility: varchar('visibility', { enum: ['public', 'private'] })\r\n    .notNull()\r\n    .default('private'),\r\n});\r\n\r\nexport type Chat = InferSelectModel<typeof chat>;\r\n\r\n// DEPRECATED: The following schema is deprecated and will be removed in the future.\r\n// Read the migration guide at https://chat-sdk.dev/docs/migration-guides/message-parts\r\nexport const messageDeprecated = pgTable('Message', {\r\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\r\n  chatId: uuid('chatId')\r\n    .notNull()\r\n    .references(() => chat.id),\r\n  role: varchar('role').notNull(),\r\n  content: json('content').notNull(),\r\n  createdAt: timestamp('createdAt').notNull(),\r\n});\r\n\r\nexport type MessageDeprecated = InferSelectModel<typeof messageDeprecated>;\r\n\r\nexport const message = pgTable('Message_v2', {\r\n  id: uuid('id').primaryKey().notNull().defaultRandom(),\r\n  chatId: uuid('chatId')\r\n    .notNull()\r\n    .references(() => chat.id),\r\n  role: varchar('role').notNull(),\r\n  parts: json('parts').notNull(),\r\n  attachments: json('attachments').notNull(),\r\n  createdAt: timestamp('createdAt').notNull(),\r\n});\r\n\r\nexport type DBMessage = InferSelectModel<typeof message>;\r\n\r\n// DEPRECATED: The following schema is deprecated and will be removed in the future.\r\n// Read the migration guide at https://chat-sdk.dev/docs/migration-guides/message-parts\r\nexport const voteDeprecated = pgTable(\r\n  'Vote',\r\n  {\r\n    chatId: uuid('chatId')\r\n      .notNull()\r\n      .references(() => chat.id),\r\n    messageId: uuid('messageId')\r\n      .notNull()\r\n      .references(() => messageDeprecated.id),\r\n    isUpvoted: boolean('isUpvoted').notNull(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      pk: primaryKey({ columns: [table.chatId, table.messageId] }),\r\n    };\r\n  },\r\n);\r\n\r\nexport type VoteDeprecated = InferSelectModel<typeof voteDeprecated>;\r\n\r\nexport const vote = pgTable(\r\n  'Vote_v2',\r\n  {\r\n    chatId: uuid('chatId')\r\n      .notNull()\r\n      .references(() => chat.id),\r\n    messageId: uuid('messageId')\r\n      .notNull()\r\n      .references(() => message.id),\r\n    isUpvoted: boolean('isUpvoted').notNull(),\r\n  },\r\n  (table) => {\r\n    return {\r\n      pk: primaryKey({ columns: [table.chatId, table.messageId] }),\r\n    };\r\n  },\r\n);\r\n\r\nexport type Vote = InferSelectModel<typeof vote>;\r\n\r\nexport const document = pgTable(\r\n  'Document',\r\n  {\r\n    id: uuid('id').notNull().defaultRandom(),\r\n    createdAt: timestamp('createdAt').notNull(),\r\n    title: text('title').notNull(),\r\n    content: text('content'),\r\n    kind: varchar('text', { enum: ['text', 'code', 'image', 'sheet'] })\r\n      .notNull()\r\n      .default('text'),\r\n    userId: uuid('userId')\r\n      .notNull()\r\n      .references(() => user.id),\r\n  },\r\n  (table) => {\r\n    return {\r\n      pk: primaryKey({ columns: [table.id, table.createdAt] }),\r\n    };\r\n  },\r\n);\r\n\r\nexport type Document = InferSelectModel<typeof document>;\r\n\r\nexport const suggestion = pgTable(\r\n  'Suggestion',\r\n  {\r\n    id: uuid('id').notNull().defaultRandom(),\r\n    documentId: uuid('documentId').notNull(),\r\n    documentCreatedAt: timestamp('documentCreatedAt').notNull(),\r\n    originalText: text('originalText').notNull(),\r\n    suggestedText: text('suggestedText').notNull(),\r\n    description: text('description'),\r\n    isResolved: boolean('isResolved').notNull().default(false),\r\n    userId: uuid('userId')\r\n      .notNull()\r\n      .references(() => user.id),\r\n    createdAt: timestamp('createdAt').notNull(),\r\n  },\r\n  (table) => ({\r\n    pk: primaryKey({ columns: [table.id] }),\r\n    documentRef: foreignKey({\r\n      columns: [table.documentId, table.documentCreatedAt],\r\n      foreignColumns: [document.id, document.createdAt],\r\n    }),\r\n  }),\r\n);\r\n\r\nexport type Suggestion = InferSelectModel<typeof suggestion>;\r\n\r\nexport const stream = pgTable(\r\n  'Stream',\r\n  {\r\n    id: uuid('id').notNull().defaultRandom(),\r\n    chatId: uuid('chatId').notNull(),\r\n    createdAt: timestamp('createdAt').notNull(),\r\n  },\r\n  (table) => ({\r\n    pk: primaryKey({ columns: [table.id] }),\r\n    chatRef: foreignKey({\r\n      columns: [table.chatId],\r\n      foreignColumns: [chat.id],\r\n    }),\r\n  }),\r\n);\r\n\r\nexport type Stream = InferSelectModel<typeof stream>;\r\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAYO,MAAM,OAAO,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAClC,IAAI,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,OAAO,GAAG,aAAa;IACnD,OAAO,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAAE,QAAQ;IAAG,GAAG,OAAO;IAC/C,UAAU,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,YAAY;QAAE,QAAQ;IAAG;AAC7C;AAIO,MAAM,OAAO,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;IAClC,IAAI,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,OAAO,GAAG,aAAa;IACnD,WAAW,CAAA,GAAA,wXAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;IACzC,OAAO,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,QAAQ,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,YAAY,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,cAAc;QAAE,MAAM;YAAC;YAAU;SAAU;IAAC,GAC7D,OAAO,GACP,OAAO,CAAC;AACb;AAMO,MAAM,oBAAoB,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EAAE,WAAW;IAClD,IAAI,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,OAAO,GAAG,aAAa;IACnD,QAAQ,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,MAAM,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IAC7B,SAAS,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAChC,WAAW,CAAA,GAAA,wXAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;AAC3C;AAIO,MAAM,UAAU,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IAC3C,IAAI,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,GAAG,OAAO,GAAG,aAAa;IACnD,QAAQ,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,MAAM,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IAC7B,OAAO,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACxC,WAAW,CAAA,GAAA,wXAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;AAC3C;AAMO,MAAM,iBAAiB,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EAClC,QACA;IACE,QAAQ,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,WAAW,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,aACb,OAAO,GACP,UAAU,CAAC,IAAM,kBAAkB,EAAE;IACxC,WAAW,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO;AACzC,GACA,CAAC;IACC,OAAO;QACL,IAAI,CAAA,GAAA,mXAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,MAAM;gBAAE,MAAM,SAAS;aAAC;QAAC;IAC5D;AACF;AAKK,MAAM,OAAO,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EACxB,WACA;IACE,QAAQ,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,WAAW,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,aACb,OAAO,GACP,UAAU,CAAC,IAAM,QAAQ,EAAE;IAC9B,WAAW,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO;AACzC,GACA,CAAC;IACC,OAAO;QACL,IAAI,CAAA,GAAA,mXAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,MAAM;gBAAE,MAAM,SAAS;aAAC;QAAC;IAC5D;AACF;AAKK,MAAM,WAAW,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EAC5B,YACA;IACE,IAAI,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,GAAG,aAAa;IACtC,WAAW,CAAA,GAAA,wXAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;IACzC,OAAO,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,SAAS,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE;IACd,MAAM,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;QAAE,MAAM;YAAC;YAAQ;YAAQ;YAAS;SAAQ;IAAC,GAC9D,OAAO,GACP,OAAO,CAAC;IACX,QAAQ,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;AAC7B,GACA,CAAC;IACC,OAAO;QACL,IAAI,CAAA,GAAA,mXAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,EAAE;gBAAE,MAAM,SAAS;aAAC;QAAC;IACxD;AACF;AAKK,MAAM,aAAa,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EAC9B,cACA;IACE,IAAI,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,GAAG,aAAa;IACtC,YAAY,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,mBAAmB,CAAA,GAAA,wXAAA,CAAA,YAAS,AAAD,EAAE,qBAAqB,OAAO;IACzD,cAAc,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,OAAO;IAC1C,eAAe,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,OAAO;IAC5C,aAAa,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE;IAClB,YAAY,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,GAAG,OAAO,CAAC;IACpD,QAAQ,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,UACV,OAAO,GACP,UAAU,CAAC,IAAM,KAAK,EAAE;IAC3B,WAAW,CAAA,GAAA,wXAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;AAC3C,GACA,CAAC,QAAU,CAAC;QACV,IAAI,CAAA,GAAA,mXAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,EAAE;aAAC;QAAC;QACrC,aAAa,CAAA,GAAA,mXAAA,CAAA,aAAU,AAAD,EAAE;YACtB,SAAS;gBAAC,MAAM,UAAU;gBAAE,MAAM,iBAAiB;aAAC;YACpD,gBAAgB;gBAAC,SAAS,EAAE;gBAAE,SAAS,SAAS;aAAC;QACnD;IACF,CAAC;AAKI,MAAM,SAAS,CAAA,GAAA,yWAAA,CAAA,UAAO,AAAD,EAC1B,UACA;IACE,IAAI,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,MAAM,OAAO,GAAG,aAAa;IACtC,QAAQ,CAAA,GAAA,mXAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,WAAW,CAAA,GAAA,wXAAA,CAAA,YAAS,AAAD,EAAE,aAAa,OAAO;AAC3C,GACA,CAAC,QAAU,CAAC;QACV,IAAI,CAAA,GAAA,mXAAA,CAAA,aAAU,AAAD,EAAE;YAAE,SAAS;gBAAC,MAAM,EAAE;aAAC;QAAC;QACrC,SAAS,CAAA,GAAA,mXAAA,CAAA,aAAU,AAAD,EAAE;YAClB,SAAS;gBAAC,MAAM,MAAM;aAAC;YACvB,gBAAgB;gBAAC,KAAK,EAAE;aAAC;QAC3B;IACF,CAAC", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/lib/errors.ts"], "sourcesContent": ["export type ErrorType =\r\n  | 'bad_request'\r\n  | 'unauthorized'\r\n  | 'forbidden'\r\n  | 'not_found'\r\n  | 'rate_limit'\r\n  | 'offline';\r\n\r\nexport type Surface =\r\n  | 'chat'\r\n  | 'auth'\r\n  | 'api'\r\n  | 'stream'\r\n  | 'database'\r\n  | 'history'\r\n  | 'vote'\r\n  | 'document'\r\n  | 'suggestions';\r\n\r\nexport type ErrorCode = `${ErrorType}:${Surface}`;\r\n\r\nexport type ErrorVisibility = 'response' | 'log' | 'none';\r\n\r\nexport const visibilityBySurface: Record<Surface, ErrorVisibility> = {\r\n  database: 'log',\r\n  chat: 'response',\r\n  auth: 'response',\r\n  stream: 'response',\r\n  api: 'response',\r\n  history: 'response',\r\n  vote: 'response',\r\n  document: 'response',\r\n  suggestions: 'response',\r\n};\r\n\r\nexport class ChatSDKError extends Error {\r\n  public type: ErrorType;\r\n  public surface: Surface;\r\n  public statusCode: number;\r\n\r\n  constructor(errorCode: ErrorCode, cause?: string) {\r\n    super();\r\n\r\n    const [type, surface] = errorCode.split(':');\r\n\r\n    this.type = type as ErrorType;\r\n    this.cause = cause;\r\n    this.surface = surface as Surface;\r\n    this.message = getMessageByErrorCode(errorCode);\r\n    this.statusCode = getStatusCodeByType(this.type);\r\n  }\r\n\r\n  public toResponse() {\r\n    const code: ErrorCode = `${this.type}:${this.surface}`;\r\n    const visibility = visibilityBySurface[this.surface];\r\n\r\n    const { message, cause, statusCode } = this;\r\n\r\n    if (visibility === 'log') {\r\n      console.error({\r\n        code,\r\n        message,\r\n        cause,\r\n      });\r\n\r\n      return Response.json(\r\n        { code: '', message: 'Something went wrong. Please try again later.' },\r\n        { status: statusCode },\r\n      );\r\n    }\r\n\r\n    return Response.json({ code, message, cause }, { status: statusCode });\r\n  }\r\n}\r\n\r\nexport function getMessageByErrorCode(errorCode: ErrorCode): string {\r\n  if (errorCode.includes('database')) {\r\n    return 'An error occurred while executing a database query.';\r\n  }\r\n\r\n  switch (errorCode) {\r\n    case 'bad_request:api':\r\n      return \"The request couldn't be processed. Please check your input and try again.\";\r\n\r\n    case 'unauthorized:auth':\r\n      return 'You need to sign in before continuing.';\r\n    case 'forbidden:auth':\r\n      return 'Your account does not have access to this feature.';\r\n\r\n    case 'rate_limit:chat':\r\n      return 'You have exceeded your maximum number of messages for the day. Please try again later.';\r\n    case 'not_found:chat':\r\n      return 'The requested chat was not found. Please check the chat ID and try again.';\r\n    case 'forbidden:chat':\r\n      return 'This chat belongs to another user. Please check the chat ID and try again.';\r\n    case 'unauthorized:chat':\r\n      return 'You need to sign in to view this chat. Please sign in and try again.';\r\n    case 'offline:chat':\r\n      return \"We're having trouble sending your message. Please check your internet connection and try again.\";\r\n\r\n    case 'not_found:document':\r\n      return 'The requested document was not found. Please check the document ID and try again.';\r\n    case 'forbidden:document':\r\n      return 'This document belongs to another user. Please check the document ID and try again.';\r\n    case 'unauthorized:document':\r\n      return 'You need to sign in to view this document. Please sign in and try again.';\r\n    case 'bad_request:document':\r\n      return 'The request to create or update the document was invalid. Please check your input and try again.';\r\n\r\n    default:\r\n      return 'Something went wrong. Please try again later.';\r\n  }\r\n}\r\n\r\nfunction getStatusCodeByType(type: ErrorType) {\r\n  switch (type) {\r\n    case 'bad_request':\r\n      return 400;\r\n    case 'unauthorized':\r\n      return 401;\r\n    case 'forbidden':\r\n      return 403;\r\n    case 'not_found':\r\n      return 404;\r\n    case 'rate_limit':\r\n      return 429;\r\n    case 'offline':\r\n      return 503;\r\n    default:\r\n      return 500;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAuBO,MAAM,sBAAwD;IACnE,UAAU;IACV,MAAM;IACN,MAAM;IACN,QAAQ;IACR,KAAK;IACL,SAAS;IACT,MAAM;IACN,UAAU;IACV,aAAa;AACf;AAEO,MAAM,qBAAqB;IACzB,KAAgB;IAChB,QAAiB;IACjB,WAAmB;IAE1B,YAAY,SAAoB,EAAE,KAAc,CAAE;QAChD,KAAK;QAEL,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAU,KAAK,CAAC;QAExC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG,sBAAsB;QACrC,IAAI,CAAC,UAAU,GAAG,oBAAoB,IAAI,CAAC,IAAI;IACjD;IAEO,aAAa;QAClB,MAAM,OAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE;QACtD,MAAM,aAAa,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC;QAEpD,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI;QAE3C,IAAI,eAAe,OAAO;YACxB,QAAQ,KAAK,CAAC;gBACZ;gBACA;gBACA;YACF;YAEA,OAAO,SAAS,IAAI,CAClB;gBAAE,MAAM;gBAAI,SAAS;YAAgD,GACrE;gBAAE,QAAQ;YAAW;QAEzB;QAEA,OAAO,SAAS,IAAI,CAAC;YAAE;YAAM;YAAS;QAAM,GAAG;YAAE,QAAQ;QAAW;IACtE;AACF;AAEO,SAAS,sBAAsB,SAAoB;IACxD,IAAI,UAAU,QAAQ,CAAC,aAAa;QAClC,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;QAET,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QAET,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QAET,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QAET;YACE,OAAO;IACX;AACF;AAEA,SAAS,oBAAoB,IAAe;IAC1C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/lib/utils.ts"], "sourcesContent": ["import type { CoreAssistantMessage, CoreToolMessage, UIMessage } from 'ai';\r\nimport { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\nimport type { Document } from '@/lib/db/schema';\r\nimport { ChatSDKError, type ErrorCode } from './errors';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const fetcher = async (url: string) => {\r\n  const response = await fetch(url);\r\n\r\n  if (!response.ok) {\r\n    const { code, cause } = await response.json();\r\n    throw new ChatSDKError(code as ErrorCode, cause);\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nexport async function fetchWithErrorHandlers(\r\n  input: RequestInfo | URL,\r\n  init?: RequestInit,\r\n) {\r\n  try {\r\n    const response = await fetch(input, init);\r\n\r\n    if (!response.ok) {\r\n      const { code, cause } = await response.json();\r\n      throw new ChatSDKError(code as <PERSON><PERSON>rCode, cause);\r\n    }\r\n\r\n    return response;\r\n  } catch (error: unknown) {\r\n    if (typeof navigator !== 'undefined' && !navigator.onLine) {\r\n      throw new ChatSDKError('offline:chat');\r\n    }\r\n\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport function getLocalStorage(key: string) {\r\n  if (typeof window !== 'undefined') {\r\n    return JSON.parse(localStorage.getItem(key) || '[]');\r\n  }\r\n  return [];\r\n}\r\n\r\nexport function generateUUID(): string {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    const r = (Math.random() * 16) | 0;\r\n    const v = c === 'x' ? r : (r & 0x3) | 0x8;\r\n    return v.toString(16);\r\n  });\r\n}\r\n\r\ntype ResponseMessageWithoutId = CoreToolMessage | CoreAssistantMessage;\r\ntype ResponseMessage = ResponseMessageWithoutId & { id: string };\r\n\r\nexport function getMostRecentUserMessage(messages: Array<UIMessage>) {\r\n  const userMessages = messages.filter((message) => message.role === 'user');\r\n  return userMessages.at(-1);\r\n}\r\n\r\nexport function getDocumentTimestampByIndex(\r\n  documents: Array<Document>,\r\n  index: number,\r\n) {\r\n  if (!documents) return new Date();\r\n  if (index > documents.length) return new Date();\r\n\r\n  return documents[index].createdAt;\r\n}\r\n\r\nexport function getTrailingMessageId({\r\n  messages,\r\n}: {\r\n  messages: Array<ResponseMessage>;\r\n}): string | null {\r\n  const trailingMessage = messages.at(-1);\r\n\r\n  if (!trailingMessage) return null;\r\n\r\n  return trailingMessage.id;\r\n}\r\n\r\nexport function sanitizeText(text: string) {\r\n  return text.replace('<has_function_call>', '');\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AACA;AACA;AAEA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,UAAU,OAAO;IAC5B,MAAM,WAAW,MAAM,MAAM;IAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI;QAC3C,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC,MAAmB;IAC5C;IAEA,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,uBACpB,KAAwB,EACxB,IAAkB;IAElB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,OAAO;QAEpC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI;YAC3C,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC,MAAmB;QAC5C;QAEA,OAAO;IACT,EAAE,OAAO,OAAgB;QACvB,IAAI,OAAO,cAAc,eAAe,CAAC,UAAU,MAAM,EAAE;YACzD,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC;QACzB;QAEA,MAAM;IACR;AACF;AAEO,SAAS,gBAAgB,GAAW;IACzC,uCAAmC;;IAEnC;IACA,OAAO,EAAE;AACX;AAEO,SAAS;IACd,OAAO,uCAAuC,OAAO,CAAC,SAAS,CAAC;QAC9D,MAAM,IAAI,AAAC,KAAK,MAAM,KAAK,KAAM;QACjC,MAAM,IAAI,MAAM,MAAM,IAAI,AAAC,IAAI,MAAO;QACtC,OAAO,EAAE,QAAQ,CAAC;IACpB;AACF;AAKO,SAAS,yBAAyB,QAA0B;IACjE,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC,UAAY,QAAQ,IAAI,KAAK;IACnE,OAAO,aAAa,EAAE,CAAC,CAAC;AAC1B;AAEO,SAAS,4BACd,SAA0B,EAC1B,KAAa;IAEb,IAAI,CAAC,WAAW,OAAO,IAAI;IAC3B,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI;IAEzC,OAAO,SAAS,CAAC,MAAM,CAAC,SAAS;AACnC;AAEO,SAAS,qBAAqB,EACnC,QAAQ,EAGT;IACC,MAAM,kBAAkB,SAAS,EAAE,CAAC,CAAC;IAErC,IAAI,CAAC,iBAAiB,OAAO;IAE7B,OAAO,gBAAgB,EAAE;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KAAK,OAAO,CAAC,uBAAuB;AAC7C", "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/lib/db/utils.ts"], "sourcesContent": ["import { generateId } from 'ai';\r\nimport { genSaltSync, hashSync } from 'bcrypt-ts';\r\n\r\nexport function generateHashedPassword(password: string) {\r\n  const salt = genSaltSync(10);\r\n  const hash = hashSync(password, salt);\r\n\r\n  return hash;\r\n}\r\n\r\nexport function generateDummyPassword() {\r\n  const password = generateId(12);\r\n  const hashedPassword = generateHashedPassword(password);\r\n\r\n  return hashedPassword;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,uBAAuB,QAAgB;IACrD,MAAM,OAAO,CAAA,GAAA,wMAAA,CAAA,cAAW,AAAD,EAAE;IACzB,MAAM,OAAO,CAAA,GAAA,wMAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;IAEhC,OAAO;AACT;AAEO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,qQAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,iBAAiB,uBAAuB;IAE9C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/lib/db/queries.ts"], "sourcesContent": ["import 'server-only';\r\n\r\nimport {\r\n  and,\r\n  asc,\r\n  count,\r\n  desc,\r\n  eq,\r\n  gt,\r\n  gte,\r\n  inArray,\r\n  lt,\r\n  type SQL,\r\n} from 'drizzle-orm';\r\nimport { drizzle } from 'drizzle-orm/postgres-js';\r\nimport postgres from 'postgres';\r\n\r\nimport {\r\n  user,\r\n  chat,\r\n  type User,\r\n  document,\r\n  type Suggestion,\r\n  suggestion,\r\n  message,\r\n  vote,\r\n  type DBMessage,\r\n  type Chat,\r\n  stream,\r\n} from './schema';\r\nimport type { ArtifactKind } from '@/components/artifact';\r\nimport { generateUUID } from '../utils';\r\nimport { generateHashedPassword } from './utils';\r\nimport type { VisibilityType } from '@/components/visibility-selector';\r\nimport { ChatSDKError } from '../errors';\r\nimport type { UserType } from '@/app/(auth)/auth';\r\n\r\n// Optionally, if not using email/pass login, you can\r\n// use the Drizzle adapter for Auth.js / NextAuth\r\n// https://authjs.dev/reference/adapter/drizzle\r\n\r\n// For TuneCatch, we'll use a mock database connection since we don't need persistent storage\r\n// In production, you would set up a real database connection\r\nconst POSTGRES_URL = process.env.POSTGRES_URL;\r\nlet client: any = null;\r\nlet db: any = null;\r\n\r\nif (POSTGRES_URL && POSTGRES_URL !== 'postgresql://localhost:5432/tunecatch?sslmode=disable') {\r\n  try {\r\n    client = postgres(POSTGRES_URL);\r\n    db = drizzle(client);\r\n  } catch (error) {\r\n    console.warn('Database connection failed, using mock data:', error);\r\n  }\r\n}\r\n\r\nexport async function getUser(email: string): Promise<Array<User>> {\r\n  // For TuneCatch, return a mock user since we don't need real authentication\r\n  if (!db) {\r\n    return [{\r\n      id: 'guest-user',\r\n      email: email,\r\n      password: '',\r\n      createdAt: new Date(),\r\n      updatedAt: new Date(),\r\n    }];\r\n  }\r\n\r\n  try {\r\n    return await db.select().from(user).where(eq(user.email, email));\r\n  } catch (error) {\r\n    // Return mock user if database fails\r\n    return [{\r\n      id: 'guest-user',\r\n      email: email,\r\n      password: '',\r\n      createdAt: new Date(),\r\n      updatedAt: new Date(),\r\n    }];\r\n  }\r\n}\r\n\r\nexport async function createUser(email: string, password: string) {\r\n  // For TuneCatch, return a mock user creation since we don't need real authentication\r\n  if (!db) {\r\n    return { id: 'guest-user', email, password: '', createdAt: new Date(), updatedAt: new Date() };\r\n  }\r\n\r\n  const hashedPassword = generateHashedPassword(password);\r\n\r\n  try {\r\n    return await db.insert(user).values({ email, password: hashedPassword });\r\n  } catch (error) {\r\n    throw new ChatSDKError('bad_request:database', 'Failed to create user');\r\n  }\r\n}\r\n\r\nexport async function createGuestUser() {\r\n  const email = `guest-${Date.now()}`;\r\n\r\n  // For TuneCatch, return a mock guest user\r\n  if (!db) {\r\n    return {\r\n      id: `guest-${Date.now()}`,\r\n      email,\r\n      type: 'guest' as UserType,\r\n    };\r\n  }\r\n\r\n  const password = generateHashedPassword(generateUUID());\r\n\r\n  try {\r\n    return await db.insert(user).values({ email, password }).returning({\r\n      id: user.id,\r\n      email: user.email,\r\n    });\r\n  } catch (error) {\r\n    // Return mock guest user if database fails\r\n    return {\r\n      id: `guest-${Date.now()}`,\r\n      email,\r\n      type: 'guest' as UserType,\r\n    };\r\n  }\r\n}\r\n\r\nexport async function saveChat({\r\n  id,\r\n  userId,\r\n  title,\r\n  visibility,\r\n}: {\r\n  id: string;\r\n  userId: string;\r\n  title: string;\r\n  visibility: VisibilityType;\r\n}) {\r\n  try {\r\n    return await db.insert(chat).values({\r\n      id,\r\n      createdAt: new Date(),\r\n      userId,\r\n      title,\r\n      visibility,\r\n    });\r\n  } catch (error) {\r\n    throw new ChatSDKError('bad_request:database', 'Failed to save chat');\r\n  }\r\n}\r\n\r\nexport async function deleteChatById({ id }: { id: string }) {\r\n  try {\r\n    await db.delete(vote).where(eq(vote.chatId, id));\r\n    await db.delete(message).where(eq(message.chatId, id));\r\n    await db.delete(stream).where(eq(stream.chatId, id));\r\n\r\n    const [chatsDeleted] = await db\r\n      .delete(chat)\r\n      .where(eq(chat.id, id))\r\n      .returning();\r\n    return chatsDeleted;\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to delete chat by id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function getChatsByUserId({\r\n  id,\r\n  limit,\r\n  startingAfter,\r\n  endingBefore,\r\n}: {\r\n  id: string;\r\n  limit: number;\r\n  startingAfter: string | null;\r\n  endingBefore: string | null;\r\n}) {\r\n  try {\r\n    const extendedLimit = limit + 1;\r\n\r\n    const query = (whereCondition?: SQL<any>) =>\r\n      db\r\n        .select()\r\n        .from(chat)\r\n        .where(\r\n          whereCondition\r\n            ? and(whereCondition, eq(chat.userId, id))\r\n            : eq(chat.userId, id),\r\n        )\r\n        .orderBy(desc(chat.createdAt))\r\n        .limit(extendedLimit);\r\n\r\n    let filteredChats: Array<Chat> = [];\r\n\r\n    if (startingAfter) {\r\n      const [selectedChat] = await db\r\n        .select()\r\n        .from(chat)\r\n        .where(eq(chat.id, startingAfter))\r\n        .limit(1);\r\n\r\n      if (!selectedChat) {\r\n        throw new ChatSDKError(\r\n          'not_found:database',\r\n          `Chat with id ${startingAfter} not found`,\r\n        );\r\n      }\r\n\r\n      filteredChats = await query(gt(chat.createdAt, selectedChat.createdAt));\r\n    } else if (endingBefore) {\r\n      const [selectedChat] = await db\r\n        .select()\r\n        .from(chat)\r\n        .where(eq(chat.id, endingBefore))\r\n        .limit(1);\r\n\r\n      if (!selectedChat) {\r\n        throw new ChatSDKError(\r\n          'not_found:database',\r\n          `Chat with id ${endingBefore} not found`,\r\n        );\r\n      }\r\n\r\n      filteredChats = await query(lt(chat.createdAt, selectedChat.createdAt));\r\n    } else {\r\n      filteredChats = await query();\r\n    }\r\n\r\n    const hasMore = filteredChats.length > limit;\r\n\r\n    return {\r\n      chats: hasMore ? filteredChats.slice(0, limit) : filteredChats,\r\n      hasMore,\r\n    };\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get chats by user id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function getChatById({ id }: { id: string }) {\r\n  try {\r\n    const [selectedChat] = await db.select().from(chat).where(eq(chat.id, id));\r\n    return selectedChat;\r\n  } catch (error) {\r\n    throw new ChatSDKError('bad_request:database', 'Failed to get chat by id');\r\n  }\r\n}\r\n\r\nexport async function saveMessages({\r\n  messages,\r\n}: {\r\n  messages: Array<DBMessage>;\r\n}) {\r\n  try {\r\n    return await db.insert(message).values(messages);\r\n  } catch (error) {\r\n    throw new ChatSDKError('bad_request:database', 'Failed to save messages');\r\n  }\r\n}\r\n\r\nexport async function getMessagesByChatId({ id }: { id: string }) {\r\n  try {\r\n    return await db\r\n      .select()\r\n      .from(message)\r\n      .where(eq(message.chatId, id))\r\n      .orderBy(asc(message.createdAt));\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get messages by chat id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function voteMessage({\r\n  chatId,\r\n  messageId,\r\n  type,\r\n}: {\r\n  chatId: string;\r\n  messageId: string;\r\n  type: 'up' | 'down';\r\n}) {\r\n  try {\r\n    const [existingVote] = await db\r\n      .select()\r\n      .from(vote)\r\n      .where(and(eq(vote.messageId, messageId)));\r\n\r\n    if (existingVote) {\r\n      return await db\r\n        .update(vote)\r\n        .set({ isUpvoted: type === 'up' })\r\n        .where(and(eq(vote.messageId, messageId), eq(vote.chatId, chatId)));\r\n    }\r\n    return await db.insert(vote).values({\r\n      chatId,\r\n      messageId,\r\n      isUpvoted: type === 'up',\r\n    });\r\n  } catch (error) {\r\n    throw new ChatSDKError('bad_request:database', 'Failed to vote message');\r\n  }\r\n}\r\n\r\nexport async function getVotesByChatId({ id }: { id: string }) {\r\n  try {\r\n    return await db.select().from(vote).where(eq(vote.chatId, id));\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get votes by chat id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function saveDocument({\r\n  id,\r\n  title,\r\n  kind,\r\n  content,\r\n  userId,\r\n}: {\r\n  id: string;\r\n  title: string;\r\n  kind: ArtifactKind;\r\n  content: string;\r\n  userId: string;\r\n}) {\r\n  try {\r\n    return await db\r\n      .insert(document)\r\n      .values({\r\n        id,\r\n        title,\r\n        kind,\r\n        content,\r\n        userId,\r\n        createdAt: new Date(),\r\n      })\r\n      .returning();\r\n  } catch (error) {\r\n    throw new ChatSDKError('bad_request:database', 'Failed to save document');\r\n  }\r\n}\r\n\r\nexport async function getDocumentsById({ id }: { id: string }) {\r\n  try {\r\n    const documents = await db\r\n      .select()\r\n      .from(document)\r\n      .where(eq(document.id, id))\r\n      .orderBy(asc(document.createdAt));\r\n\r\n    return documents;\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get documents by id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function getDocumentById({ id }: { id: string }) {\r\n  try {\r\n    const [selectedDocument] = await db\r\n      .select()\r\n      .from(document)\r\n      .where(eq(document.id, id))\r\n      .orderBy(desc(document.createdAt));\r\n\r\n    return selectedDocument;\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get document by id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function deleteDocumentsByIdAfterTimestamp({\r\n  id,\r\n  timestamp,\r\n}: {\r\n  id: string;\r\n  timestamp: Date;\r\n}) {\r\n  try {\r\n    await db\r\n      .delete(suggestion)\r\n      .where(\r\n        and(\r\n          eq(suggestion.documentId, id),\r\n          gt(suggestion.documentCreatedAt, timestamp),\r\n        ),\r\n      );\r\n\r\n    return await db\r\n      .delete(document)\r\n      .where(and(eq(document.id, id), gt(document.createdAt, timestamp)))\r\n      .returning();\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to delete documents by id after timestamp',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function saveSuggestions({\r\n  suggestions,\r\n}: {\r\n  suggestions: Array<Suggestion>;\r\n}) {\r\n  try {\r\n    return await db.insert(suggestion).values(suggestions);\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to save suggestions',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function getSuggestionsByDocumentId({\r\n  documentId,\r\n}: {\r\n  documentId: string;\r\n}) {\r\n  try {\r\n    return await db\r\n      .select()\r\n      .from(suggestion)\r\n      .where(and(eq(suggestion.documentId, documentId)));\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get suggestions by document id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function getMessageById({ id }: { id: string }) {\r\n  try {\r\n    return await db.select().from(message).where(eq(message.id, id));\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get message by id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function deleteMessagesByChatIdAfterTimestamp({\r\n  chatId,\r\n  timestamp,\r\n}: {\r\n  chatId: string;\r\n  timestamp: Date;\r\n}) {\r\n  try {\r\n    const messagesToDelete = await db\r\n      .select({ id: message.id })\r\n      .from(message)\r\n      .where(\r\n        and(eq(message.chatId, chatId), gte(message.createdAt, timestamp)),\r\n      );\r\n\r\n    const messageIds = messagesToDelete.map((message) => message.id);\r\n\r\n    if (messageIds.length > 0) {\r\n      await db\r\n        .delete(vote)\r\n        .where(\r\n          and(eq(vote.chatId, chatId), inArray(vote.messageId, messageIds)),\r\n        );\r\n\r\n      return await db\r\n        .delete(message)\r\n        .where(\r\n          and(eq(message.chatId, chatId), inArray(message.id, messageIds)),\r\n        );\r\n    }\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to delete messages by chat id after timestamp',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function updateChatVisiblityById({\r\n  chatId,\r\n  visibility,\r\n}: {\r\n  chatId: string;\r\n  visibility: 'private' | 'public';\r\n}) {\r\n  try {\r\n    return await db.update(chat).set({ visibility }).where(eq(chat.id, chatId));\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to update chat visibility by id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function getMessageCountByUserId({\r\n  id,\r\n  differenceInHours,\r\n}: { id: string; differenceInHours: number }) {\r\n  try {\r\n    const twentyFourHoursAgo = new Date(\r\n      Date.now() - differenceInHours * 60 * 60 * 1000,\r\n    );\r\n\r\n    const [stats] = await db\r\n      .select({ count: count(message.id) })\r\n      .from(message)\r\n      .innerJoin(chat, eq(message.chatId, chat.id))\r\n      .where(\r\n        and(\r\n          eq(chat.userId, id),\r\n          gte(message.createdAt, twentyFourHoursAgo),\r\n          eq(message.role, 'user'),\r\n        ),\r\n      )\r\n      .execute();\r\n\r\n    return stats?.count ?? 0;\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get message count by user id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function createStreamId({\r\n  streamId,\r\n  chatId,\r\n}: {\r\n  streamId: string;\r\n  chatId: string;\r\n}) {\r\n  try {\r\n    await db\r\n      .insert(stream)\r\n      .values({ id: streamId, chatId, createdAt: new Date() });\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to create stream id',\r\n    );\r\n  }\r\n}\r\n\r\nexport async function getStreamIdsByChatId({ chatId }: { chatId: string }) {\r\n  try {\r\n    const streamIds = await db\r\n      .select({ id: stream.id })\r\n      .from(stream)\r\n      .where(eq(stream.chatId, chatId))\r\n      .orderBy(asc(stream.createdAt))\r\n      .execute();\r\n\r\n    return streamIds.map(({ id }) => id);\r\n  } catch (error) {\r\n    throw new ChatSDKError(\r\n      'bad_request:database',\r\n      'Failed to get stream ids by chat id',\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AAAA;AAAA;AAYA;AACA;AAEA;AAcA;AACA;AAEA;;;;;;;;;AAGA,qDAAqD;AACrD,iDAAiD;AACjD,+CAA+C;AAE/C,6FAA6F;AAC7F,6DAA6D;AAC7D,MAAM,eAAe,QAAQ,GAAG,CAAC,YAAY;AAC7C,IAAI,SAAc;AAClB,IAAI,KAAU;AAEd,IAAI,gBAAgB,iBAAiB,yDAAyD;IAC5F,IAAI;QACF,SAAS,CAAA,GAAA,+LAAA,CAAA,UAAQ,AAAD,EAAE;QAClB,KAAK,CAAA,GAAA,8WAAA,CAAA,UAAO,AAAD,EAAE;IACf,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,gDAAgD;IAC/D;AACF;AAEO,eAAe,QAAQ,KAAa;IACzC,4EAA4E;IAC5E,IAAI,CAAC,IAAI;QACP,OAAO;YAAC;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;SAAE;IACJ;IAEA,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,KAAK,EAAE;IAC3D,EAAE,OAAO,OAAO;QACd,qCAAqC;QACrC,OAAO;YAAC;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;SAAE;IACJ;AACF;AAEO,eAAe,WAAW,KAAa,EAAE,QAAgB;IAC9D,qFAAqF;IACrF,IAAI,CAAC,IAAI;QACP,OAAO;YAAE,IAAI;YAAc;YAAO,UAAU;YAAI,WAAW,IAAI;YAAQ,WAAW,IAAI;QAAO;IAC/F;IAEA,MAAM,iBAAiB,CAAA,GAAA,oHAAA,CAAA,yBAAsB,AAAD,EAAE;IAE9C,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,MAAM,CAAC;YAAE;YAAO,UAAU;QAAe;IACxE,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC,wBAAwB;IACjD;AACF;AAEO,eAAe;IACpB,MAAM,QAAQ,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;IAEnC,0CAA0C;IAC1C,IAAI,CAAC,IAAI;QACP,OAAO;YACL,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB;YACA,MAAM;QACR;IACF;IAEA,MAAM,WAAW,CAAA,GAAA,oHAAA,CAAA,yBAAsB,AAAD,EAAE,CAAA,GAAA,8GAAA,CAAA,eAAY,AAAD;IAEnD,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,MAAM,CAAC;YAAE;YAAO;QAAS,GAAG,SAAS,CAAC;YACjE,IAAI,qHAAA,CAAA,OAAI,CAAC,EAAE;YACX,OAAO,qHAAA,CAAA,OAAI,CAAC,KAAK;QACnB;IACF,EAAE,OAAO,OAAO;QACd,2CAA2C;QAC3C,OAAO;YACL,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB;YACA,MAAM;QACR;IACF;AACF;AAEO,eAAe,SAAS,EAC7B,EAAE,EACF,MAAM,EACN,KAAK,EACL,UAAU,EAMX;IACC,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,MAAM,CAAC;YAClC;YACA,WAAW,IAAI;YACf;YACA;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC,wBAAwB;IACjD;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,EAAkB;IACzD,IAAI;QACF,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE;QAC5C,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,UAAO,EAAE,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE;QAClD,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,SAAM,EAAE,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,SAAM,CAAC,MAAM,EAAE;QAEhD,MAAM,CAAC,aAAa,GAAG,MAAM,GAC1B,MAAM,CAAC,qHAAA,CAAA,OAAI,EACX,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,EAAE,KAClB,SAAS;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,iBAAiB,EACrC,EAAE,EACF,KAAK,EACL,aAAa,EACb,YAAY,EAMb;IACC,IAAI;QACF,MAAM,gBAAgB,QAAQ;QAE9B,MAAM,QAAQ,CAAC,iBACb,GACG,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,OAAI,EACT,KAAK,CACJ,iBACI,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,gBAAgB,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE,OACpC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE,KAErB,OAAO,CAAC,CAAA,GAAA,kXAAA,CAAA,OAAI,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS,GAC3B,KAAK,CAAC;QAEX,IAAI,gBAA6B,EAAE;QAEnC,IAAI,eAAe;YACjB,MAAM,CAAC,aAAa,GAAG,MAAM,GAC1B,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,OAAI,EACT,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,EAAE,gBAClB,KAAK,CAAC;YAET,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,sBACA,CAAC,aAAa,EAAE,cAAc,UAAU,CAAC;YAE7C;YAEA,gBAAgB,MAAM,MAAM,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS,EAAE,aAAa,SAAS;QACvE,OAAO,IAAI,cAAc;YACvB,MAAM,CAAC,aAAa,GAAG,MAAM,GAC1B,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,OAAI,EACT,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,EAAE,eAClB,KAAK,CAAC;YAET,IAAI,CAAC,cAAc;gBACjB,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,sBACA,CAAC,aAAa,EAAE,aAAa,UAAU,CAAC;YAE5C;YAEA,gBAAgB,MAAM,MAAM,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS,EAAE,aAAa,SAAS;QACvE,OAAO;YACL,gBAAgB,MAAM;QACxB;QAEA,MAAM,UAAU,cAAc,MAAM,GAAG;QAEvC,OAAO;YACL,OAAO,UAAU,cAAc,KAAK,CAAC,GAAG,SAAS;YACjD;QACF;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,YAAY,EAAE,EAAE,EAAkB;IACtD,IAAI;QACF,MAAM,CAAC,aAAa,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,EAAE;QACtE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC,wBAAwB;IACjD;AACF;AAEO,eAAe,aAAa,EACjC,QAAQ,EAGT;IACC,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,UAAO,EAAE,MAAM,CAAC;IACzC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC,wBAAwB;IACjD;AACF;AAEO,eAAe,oBAAoB,EAAE,EAAE,EAAkB;IAC9D,IAAI;QACF,OAAO,MAAM,GACV,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,UAAO,EACZ,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,KACzB,OAAO,CAAC,CAAA,GAAA,kXAAA,CAAA,MAAG,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,SAAS;IAClC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,YAAY,EAChC,MAAM,EACN,SAAS,EACT,IAAI,EAKL;IACC,IAAI;QACF,MAAM,CAAC,aAAa,GAAG,MAAM,GAC1B,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,OAAI,EACT,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS,EAAE;QAEhC,IAAI,cAAc;YAChB,OAAO,MAAM,GACV,MAAM,CAAC,qHAAA,CAAA,OAAI,EACX,GAAG,CAAC;gBAAE,WAAW,SAAS;YAAK,GAC/B,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS,EAAE,YAAY,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE;QAC9D;QACA,OAAO,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,MAAM,CAAC;YAClC;YACA;YACA,WAAW,SAAS;QACtB;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC,wBAAwB;IACjD;AACF;AAEO,eAAe,iBAAiB,EAAE,EAAE,EAAkB;IAC3D,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,OAAI,EAAE,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE;IAC5D,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,aAAa,EACjC,EAAE,EACF,KAAK,EACL,IAAI,EACJ,OAAO,EACP,MAAM,EAOP;IACC,IAAI;QACF,OAAO,MAAM,GACV,MAAM,CAAC,qHAAA,CAAA,WAAQ,EACf,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA,WAAW,IAAI;QACjB,GACC,SAAS;IACd,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CAAC,wBAAwB;IACjD;AACF;AAEO,eAAe,iBAAiB,EAAE,EAAE,EAAkB;IAC3D,IAAI;QACF,MAAM,YAAY,MAAM,GACrB,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,WAAQ,EACb,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,KACtB,OAAO,CAAC,CAAA,GAAA,kXAAA,CAAA,MAAG,AAAD,EAAE,qHAAA,CAAA,WAAQ,CAAC,SAAS;QAEjC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,gBAAgB,EAAE,EAAE,EAAkB;IAC1D,IAAI;QACF,MAAM,CAAC,iBAAiB,GAAG,MAAM,GAC9B,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,WAAQ,EACb,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,KACtB,OAAO,CAAC,CAAA,GAAA,kXAAA,CAAA,OAAI,AAAD,EAAE,qHAAA,CAAA,WAAQ,CAAC,SAAS;QAElC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,kCAAkC,EACtD,EAAE,EACF,SAAS,EAIV;IACC,IAAI;QACF,MAAM,GACH,MAAM,CAAC,qHAAA,CAAA,aAAU,EACjB,KAAK,CACJ,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,aAAU,CAAC,UAAU,EAAE,KAC1B,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,aAAU,CAAC,iBAAiB,EAAE;QAIvC,OAAO,MAAM,GACV,MAAM,CAAC,qHAAA,CAAA,WAAQ,EACf,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,WAAQ,CAAC,EAAE,EAAE,KAAK,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,WAAQ,CAAC,SAAS,EAAE,aACtD,SAAS;IACd,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,gBAAgB,EACpC,WAAW,EAGZ;IACC,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,aAAU,EAAE,MAAM,CAAC;IAC5C,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,2BAA2B,EAC/C,UAAU,EAGX;IACC,IAAI;QACF,OAAO,MAAM,GACV,MAAM,GACN,IAAI,CAAC,qHAAA,CAAA,aAAU,EACf,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,aAAU,CAAC,UAAU,EAAE;IACzC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,eAAe,EAAE,EAAE,EAAkB;IACzD,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,qHAAA,CAAA,UAAO,EAAE,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,EAAE,EAAE;IAC9D,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,qCAAqC,EACzD,MAAM,EACN,SAAS,EAIV;IACC,IAAI;QACF,MAAM,mBAAmB,MAAM,GAC5B,MAAM,CAAC;YAAE,IAAI,qHAAA,CAAA,UAAO,CAAC,EAAE;QAAC,GACxB,IAAI,CAAC,qHAAA,CAAA,UAAO,EACZ,KAAK,CACJ,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,SAAS,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,SAAS,EAAE;QAG3D,MAAM,aAAa,iBAAiB,GAAG,CAAC,CAAC,UAAY,QAAQ,EAAE;QAE/D,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,MAAM,GACH,MAAM,CAAC,qHAAA,CAAA,OAAI,EACX,KAAK,CACJ,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE,SAAS,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,SAAS,EAAE;YAGzD,OAAO,MAAM,GACV,MAAM,CAAC,qHAAA,CAAA,UAAO,EACd,KAAK,CACJ,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,SAAS,CAAA,GAAA,sXAAA,CAAA,UAAO,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAE1D;IACF,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,wBAAwB,EAC5C,MAAM,EACN,UAAU,EAIX;IACC,IAAI;QACF,OAAO,MAAM,GAAG,MAAM,CAAC,qHAAA,CAAA,OAAI,EAAE,GAAG,CAAC;YAAE;QAAW,GAAG,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,EAAE;IACrE,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,wBAAwB,EAC5C,EAAE,EACF,iBAAiB,EACyB;IAC1C,IAAI;QACF,MAAM,qBAAqB,IAAI,KAC7B,KAAK,GAAG,KAAK,oBAAoB,KAAK,KAAK;QAG7C,MAAM,CAAC,MAAM,GAAG,MAAM,GACnB,MAAM,CAAC;YAAE,OAAO,CAAA,GAAA,mXAAA,CAAA,QAAK,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,EAAE;QAAE,GAClC,IAAI,CAAC,qHAAA,CAAA,UAAO,EACZ,SAAS,CAAC,qHAAA,CAAA,OAAI,EAAE,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,MAAM,EAAE,qHAAA,CAAA,OAAI,CAAC,EAAE,GAC1C,KAAK,CACJ,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,OAAI,CAAC,MAAM,EAAE,KAChB,CAAA,GAAA,sXAAA,CAAA,MAAG,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,SAAS,EAAE,qBACvB,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,UAAO,CAAC,IAAI,EAAE,UAGpB,OAAO;QAEV,OAAO,OAAO,SAAS;IACzB,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,eAAe,EACnC,QAAQ,EACR,MAAM,EAIP;IACC,IAAI;QACF,MAAM,GACH,MAAM,CAAC,qHAAA,CAAA,SAAM,EACb,MAAM,CAAC;YAAE,IAAI;YAAU;YAAQ,WAAW,IAAI;QAAO;IAC1D,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF;AAEO,eAAe,qBAAqB,EAAE,MAAM,EAAsB;IACvE,IAAI;QACF,MAAM,YAAY,MAAM,GACrB,MAAM,CAAC;YAAE,IAAI,qHAAA,CAAA,SAAM,CAAC,EAAE;QAAC,GACvB,IAAI,CAAC,qHAAA,CAAA,SAAM,EACX,KAAK,CAAC,CAAA,GAAA,sXAAA,CAAA,KAAE,AAAD,EAAE,qHAAA,CAAA,SAAM,CAAC,MAAM,EAAE,SACxB,OAAO,CAAC,CAAA,GAAA,kXAAA,CAAA,MAAG,AAAD,EAAE,qHAAA,CAAA,SAAM,CAAC,SAAS,GAC5B,OAAO;QAEV,OAAO,UAAU,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,GAAK;IACnC,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,+GAAA,CAAA,eAAY,CACpB,wBACA;IAEJ;AACF", "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/app/%28auth%29/auth.config.ts"], "sourcesContent": ["import type { NextAuthConfig } from 'next-auth';\r\n\r\nexport const authConfig = {\r\n  pages: {\r\n    signIn: '/login',\r\n    newUser: '/',\r\n  },\r\n  providers: [\r\n    // added later in auth.ts since it requires bcrypt which is only compatible with Node.js\r\n    // while this file is also used in non-Node.js environments\r\n  ],\r\n  callbacks: {},\r\n} satisfies NextAuthConfig;\r\n"], "names": [], "mappings": ";;;AAEO,MAAM,aAAa;IACxB,OAAO;QACL,QAAQ;QACR,SAAS;IACX;IACA,WAAW,EAGV;IACD,WAAW,CAAC;AACd", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/lib/constants.ts"], "sourcesContent": ["import { generateDummyPassword } from './db/utils';\r\n\r\nexport const isProductionEnvironment = process.env.NODE_ENV === 'production';\r\nexport const isDevelopmentEnvironment = process.env.NODE_ENV === 'development';\r\nexport const isTestEnvironment = Boolean(\r\n  process.env.PLAYWRIGHT_TEST_BASE_URL ||\r\n    process.env.PLAYWRIGHT ||\r\n    process.env.CI_PLAYWRIGHT,\r\n);\r\n\r\nexport const guestRegex = /^guest-\\d+$/;\r\n\r\nexport const DUMMY_PASSWORD = generateDummyPassword();\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEO,MAAM,0BAA0B,oDAAyB;AACzD,MAAM,2BAA2B,oDAAyB;AAC1D,MAAM,oBAAoB,QAC/B,QAAQ,GAAG,CAAC,wBAAwB,IAClC,QAAQ,GAAG,CAAC,UAAU,IACtB,QAAQ,GAAG,CAAC,aAAa;AAGtB,MAAM,aAAa;AAEnB,MAAM,iBAAiB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/app/%28auth%29/auth.ts"], "sourcesContent": ["import { compare } from 'bcrypt-ts';\r\nimport NextAuth, { type DefaultSession } from 'next-auth';\r\nimport Credentials from 'next-auth/providers/credentials';\r\nimport { createGuestUser, getUser } from '@/lib/db/queries';\r\nimport { authConfig } from './auth.config';\r\nimport { DUMMY_PASSWORD } from '@/lib/constants';\r\nimport type { DefaultJWT } from 'next-auth/jwt';\r\n\r\nexport type UserType = 'guest' | 'regular';\r\n\r\ndeclare module 'next-auth' {\r\n  interface Session extends DefaultSession {\r\n    user: {\r\n      id: string;\r\n      type: UserType;\r\n    } & DefaultSession['user'];\r\n  }\r\n\r\n  interface User {\r\n    id?: string;\r\n    email?: string | null;\r\n    type: UserType;\r\n  }\r\n}\r\n\r\ndeclare module 'next-auth/jwt' {\r\n  interface JWT extends DefaultJWT {\r\n    id: string;\r\n    type: UserType;\r\n  }\r\n}\r\n\r\nexport const {\r\n  handlers: { GET, POST },\r\n  auth,\r\n  signIn,\r\n  signOut,\r\n} = NextAuth({\r\n  ...authConfig,\r\n  providers: [\r\n    Credentials({\r\n      credentials: {},\r\n      async authorize({ email, password }: any) {\r\n        const users = await getUser(email);\r\n\r\n        if (users.length === 0) {\r\n          await compare(password, DUMMY_PASSWORD);\r\n          return null;\r\n        }\r\n\r\n        const [user] = users;\r\n\r\n        if (!user.password) {\r\n          await compare(password, DUMMY_PASSWORD);\r\n          return null;\r\n        }\r\n\r\n        const passwordsMatch = await compare(password, user.password);\r\n\r\n        if (!passwordsMatch) return null;\r\n\r\n        return { ...user, type: 'regular' };\r\n      },\r\n    }),\r\n    Credentials({\r\n      id: 'guest',\r\n      credentials: {},\r\n      async authorize() {\r\n        const [guestUser] = await createGuestUser();\r\n        return { ...guestUser, type: 'guest' };\r\n      },\r\n    }),\r\n  ],\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.id = user.id as string;\r\n        token.type = user.type;\r\n      }\r\n\r\n      return token;\r\n    },\r\n    async session({ session, token }) {\r\n      if (session.user) {\r\n        session.user.id = token.id;\r\n        session.user.type = token.type;\r\n      }\r\n\r\n      return session;\r\n    },\r\n  },\r\n});\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AA2BO,MAAM,EACX,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,EACvB,IAAI,EACJ,MAAM,EACN,OAAO,EACR,GAAG,CAAA,GAAA,4WAAA,CAAA,UAAQ,AAAD,EAAE;IACX,GAAG,mIAAA,CAAA,aAAU;IACb,WAAW;QACT,CAAA,GAAA,wNAAA,CAAA,UAAW,AAAD,EAAE;YACV,aAAa,CAAC;YACd,MAAM,WAAU,EAAE,KAAK,EAAE,QAAQ,EAAO;gBACtC,MAAM,QAAQ,MAAM,CAAA,GAAA,sHAAA,CAAA,UAAO,AAAD,EAAE;gBAE5B,IAAI,MAAM,MAAM,KAAK,GAAG;oBACtB,MAAM,CAAA,GAAA,wMAAA,CAAA,UAAO,AAAD,EAAE,UAAU,kHAAA,CAAA,iBAAc;oBACtC,OAAO;gBACT;gBAEA,MAAM,CAAC,KAAK,GAAG;gBAEf,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAClB,MAAM,CAAA,GAAA,wMAAA,CAAA,UAAO,AAAD,EAAE,UAAU,kHAAA,CAAA,iBAAc;oBACtC,OAAO;gBACT;gBAEA,MAAM,iBAAiB,MAAM,CAAA,GAAA,wMAAA,CAAA,UAAO,AAAD,EAAE,UAAU,KAAK,QAAQ;gBAE5D,IAAI,CAAC,gBAAgB,OAAO;gBAE5B,OAAO;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAU;YACpC;QACF;QACA,CAAA,GAAA,wNAAA,CAAA,UAAW,AAAD,EAAE;YACV,IAAI;YACJ,aAAa,CAAC;YACd,MAAM;gBACJ,MAAM,CAAC,UAAU,GAAG,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD;gBACxC,OAAO;oBAAE,GAAG,SAAS;oBAAE,MAAM;gBAAQ;YACvC;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,EAAE,GAAG,KAAK,EAAE;gBAClB,MAAM,IAAI,GAAG,KAAK,IAAI;YACxB;YAEA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,EAAE;gBAChB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YAEA,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/TuneCatch/app/%28auth%29/api/auth/guest/route.ts"], "sourcesContent": ["import { signIn } from '@/app/(auth)/auth';\r\nimport { isDevelopmentEnvironment } from '@/lib/constants';\r\nimport { getToken } from 'next-auth/jwt';\r\nimport { NextResponse } from 'next/server';\r\n\r\nexport async function GET(request: Request) {\r\n  const { searchParams } = new URL(request.url);\r\n  const redirectUrl = searchParams.get('redirectUrl') || '/';\r\n\r\n  const token = await getToken({\r\n    req: request,\r\n    secret: process.env.AUTH_SECRET,\r\n    secureCookie: !isDevelopmentEnvironment,\r\n  });\r\n\r\n  if (token) {\r\n    return NextResponse.redirect(new URL('/', request.url));\r\n  }\r\n\r\n  return signIn('guest', { redirect: true, redirectTo: redirectUrl });\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AACA;;;;;AAEO,eAAe,IAAI,OAAgB;IACxC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,cAAc,aAAa,GAAG,CAAC,kBAAkB;IAEvD,MAAM,QAAQ,MAAM,CAAA,GAAA,mMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B,KAAK;QACL,QAAQ,QAAQ,GAAG,CAAC,WAAW;QAC/B,cAAc,CAAC,kHAAA,CAAA,2BAAwB;IACzC;IAEA,IAAI,OAAO;QACT,OAAO,qVAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;IACvD;IAEA,OAAO,CAAA,GAAA,yHAAA,CAAA,SAAM,AAAD,EAAE,SAAS;QAAE,UAAU;QAAM,YAAY;IAAY;AACnE", "debugId": null}}]}
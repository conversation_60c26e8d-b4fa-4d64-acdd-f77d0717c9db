{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_16661361._.js", "server/edge/chunks/[root-of-the-server]__c19a4267._.js", "server/edge/chunks/edge-wrapper_fdf6200d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/chat/:id{(\\\\.json)}?", "originalSource": "/chat/:id"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/login{(\\\\.json)}?", "originalSource": "/login"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/register{(\\\\.json)}?", "originalSource": "/register"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PX4kcRPfRvhJM2r5SL2SevBXS4qV1mzNXSBYVrVA9nw=", "__NEXT_PREVIEW_MODE_ID": "d13b5b4c7abed9e26b7a07f6c20d3e63", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7e0830d5c37df9850410ffa8911f8fe7334258fdd9802bda3383ad1a7c6165d0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9cde822f3da8ff2b2053e9e9910fecf852b3202e43be3da32bb6548775854d51"}}}, "instrumentation": null, "functions": {}}
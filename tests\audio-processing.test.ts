/**
 * Tests for audio processing functionality
 */

import { describe, it, expect, beforeEach } from '@jest/globals';
import { audioToBase64, getAudioFeatures, validateAudioForIdentification } from '../lib/audio-processing';

// Mock audio blob for testing
const createMockAudioBlob = (duration: number = 10): Blob => {
  // Create a simple WAV header for testing
  const sampleRate = 44100;
  const channels = 1;
  const bitsPerSample = 16;
  const samples = duration * sampleRate;
  const dataSize = samples * channels * (bitsPerSample / 8);
  const fileSize = 44 + dataSize;

  const buffer = new ArrayBuffer(fileSize);
  const view = new DataView(buffer);

  // WAV header
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };

  writeString(0, 'RIFF');
  view.setUint32(4, fileSize - 8, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, channels, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * channels * (bitsPerSample / 8), true);
  view.setUint16(32, channels * (bitsPerSample / 8), true);
  view.setUint16(34, bitsPerSample, true);
  writeString(36, 'data');
  view.setUint32(40, dataSize, true);

  // Add some mock audio data (sine wave)
  for (let i = 0; i < samples; i++) {
    const sample = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.5;
    view.setInt16(44 + i * 2, sample * 32767, true);
  }

  return new Blob([buffer], { type: 'audio/wav' });
};

describe('Audio Processing', () => {
  describe('audioToBase64', () => {
    it('should convert audio blob to base64', async () => {
      const blob = createMockAudioBlob(1);
      const base64 = await audioToBase64(blob);
      
      expect(typeof base64).toBe('string');
      expect(base64.length).toBeGreaterThan(0);
      expect(base64).not.toContain('data:');
    });
  });

  describe('validateAudioForIdentification', () => {
    it('should validate good audio features', () => {
      const features = {
        duration: 15,
        sampleRate: 44100,
        channels: 1,
        format: 'audio/wav',
        size: 1024 * 100, // 100KB
      };

      const result = validateAudioForIdentification(features);
      expect(result.isValid).toBe(true);
      expect(result.issues).toHaveLength(0);
    });

    it('should reject audio that is too short', () => {
      const features = {
        duration: 3,
        sampleRate: 44100,
        channels: 1,
        format: 'audio/wav',
        size: 1024 * 50,
      };

      const result = validateAudioForIdentification(features);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Recording too short (minimum 5 seconds recommended)');
    });

    it('should reject audio that is too long', () => {
      const features = {
        duration: 70,
        sampleRate: 44100,
        channels: 1,
        format: 'audio/wav',
        size: 1024 * 1000,
      };

      const result = validateAudioForIdentification(features);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Recording too long (maximum 60 seconds recommended)');
    });

    it('should reject audio file that is too large', () => {
      const features = {
        duration: 30,
        sampleRate: 44100,
        channels: 1,
        format: 'audio/wav',
        size: 1024 * 1024 * 15, // 15MB
      };

      const result = validateAudioForIdentification(features);
      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('File size too large');
    });
  });
});

describe('Song Identification', () => {
  it('should return mock song matches', async () => {
    // This would test the actual song identification
    // For now, we'll just verify the structure
    const mockAudioBase64 = 'UklGRiQAAABXQVZFZm10IBAAAAABAAEA';
    
    // In a real test, you would call the identification function
    // const result = await identifySong.execute({
    //   audioBase64: mockAudioBase64,
    //   audioFormat: 'audio/wav',
    //   duration: 10,
    // });
    
    // For now, just verify the mock structure
    expect(mockAudioBase64).toBeDefined();
  });
});

// Integration test for the complete workflow
describe('Complete Workflow', () => {
  it('should process audio end-to-end', async () => {
    const mockBlob = createMockAudioBlob(10);
    
    // Test audio to base64 conversion
    const base64 = await audioToBase64(mockBlob);
    expect(base64).toBeDefined();
    
    // Test feature extraction would go here
    // const features = await getAudioFeatures(mockBlob);
    // expect(features.duration).toBeCloseTo(10, 1);
    
    // Test validation
    const mockFeatures = {
      duration: 10,
      sampleRate: 44100,
      channels: 1,
      format: 'audio/wav',
      size: mockBlob.size,
    };
    
    const validation = validateAudioForIdentification(mockFeatures);
    expect(validation.isValid).toBe(true);
  });
});
